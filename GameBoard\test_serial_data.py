#!/usr/bin/env python3
"""
串口数据测试工具
用于检查是否能正确接收摇杆数据
"""

import serial
import serial.tools.list_ports
import time
import sys

def find_arduino_port():
    """查找Arduino端口"""
    print("🔍 查找Arduino端口...")
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("❌ 没有找到任何串口设备")
        return None
    
    print(f"发现 {len(ports)} 个串口设备:")
    for i, port in enumerate(ports, 1):
        print(f"  {i}. {port.device} - {port.description}")
    
    # 优先尝试包含 Arduino 关键词的端口
    arduino_ports = []
    other_ports = []
    
    for port in ports:
        description = port.description.lower()
        if any(keyword in description for keyword in ['arduino', 'ch340', 'ch341', 'cp210', 'ftdi']):
            arduino_ports.append(port)
        else:
            other_ports.append(port)
    
    # 先尝试 Arduino 相关端口
    all_ports = arduino_ports + other_ports
    
    for port in all_ports:
        try:
            print(f"🔌 尝试连接: {port.device} ({port.description})")
            test_serial = serial.Serial(port.device, 115200, timeout=1)
            print(f"✅ 成功连接到: {port.device}")
            return test_serial
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
            continue
    
    print("❌ 所有端口都无法连接")
    return None

def test_serial_communication():
    """测试串口通信"""
    print("=" * 50)
    print("🔍 串口数据测试工具")
    print("=" * 50)
    
    # 连接串口
    serial_port = find_arduino_port()
    if not serial_port:
        print("❌ 无法连接到Arduino")
        return
    
    print("\n⚠️  请操作摇杆，观察是否有数据接收")
    print("按 Ctrl+C 停止测试")
    print("-" * 50)
    
    try:
        data_count = 0
        start_time = time.time()
        
        while True:
            try:
                if serial_port.in_waiting:
                    data = serial_port.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        data_count += 1
                        current_time = time.time() - start_time
                        print(f"[{current_time:.1f}s] 📡 接收到数据: {data}")
                        
                        # 分析数据类型
                        if "摇杆位置" in data:
                            print(f"   → 位置数据")
                        elif "摇杆：" in data:
                            print(f"   → 方向数据")
                        elif "按下" in data:
                            print(f"   → 按键数据")
                        else:
                            print(f"   → 其他数据")
                
                # 每5秒显示一次统计
                if int(time.time() - start_time) % 5 == 0 and int(time.time() - start_time) > 0:
                    if data_count == 0:
                        print(f"⚠️  {int(time.time() - start_time)}秒内未接收到任何数据")
                    time.sleep(1)  # 避免重复显示
                
                time.sleep(0.01)
                
            except Exception as e:
                print(f"❌ 读取数据错误: {e}")
                break
                
    except KeyboardInterrupt:
        print(f"\n\n🛑 测试停止")
        print(f"📊 总共接收到 {data_count} 条数据")
        
        if data_count == 0:
            print("\n🔍 故障排除建议:")
            print("1. 检查Arduino是否正确连接")
            print("2. 确认Arduino上传了正确的摇杆程序")
            print("3. 检查摇杆是否正确连接到Arduino")
            print("4. 尝试重新插拔USB线")
            print("5. 检查Arduino串口监视器是否能看到数据")
        else:
            print("✅ 串口通信正常")
    
    finally:
        if serial_port:
            serial_port.close()
            print("✅ 串口已关闭")

def test_specific_port():
    """测试指定端口"""
    print("\n🔧 手动指定端口测试")
    
    ports = serial.tools.list_ports.comports()
    if not ports:
        print("❌ 没有可用端口")
        return
    
    print("可用端口:")
    for i, port in enumerate(ports, 1):
        print(f"  {i}. {port.device} - {port.description}")
    
    try:
        choice = input(f"\n请选择端口 (1-{len(ports)}): ").strip()
        if choice.isdigit():
            index = int(choice) - 1
            if 0 <= index < len(ports):
                selected_port = ports[index]
                print(f"尝试连接到: {selected_port.device}")
                
                test_serial = serial.Serial(selected_port.device, 115200, timeout=1)
                print(f"✅ 连接成功")
                
                print("监听数据中... (按 Ctrl+C 停止)")
                try:
                    while True:
                        if test_serial.in_waiting:
                            data = test_serial.readline().decode('utf-8', errors='ignore').strip()
                            if data:
                                print(f"📡 {data}")
                        time.sleep(0.01)
                except KeyboardInterrupt:
                    print("\n测试停止")
                finally:
                    test_serial.close()
            else:
                print("❌ 无效选择")
        else:
            print("❌ 请输入数字")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    print("选择测试模式:")
    print("1. 自动查找并测试")
    print("2. 手动选择端口测试")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        test_serial_communication()
    elif choice == "2":
        test_specific_port()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
