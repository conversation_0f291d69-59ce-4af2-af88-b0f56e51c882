#!/usr/bin/env python3
"""
JoystickShield PC 控制器 - 游戏专用版本
使用 Win32 API 直接发送按键，最大化游戏兼容性
"""

import serial
import serial.tools.list_ports
import time
import threading
import sys
from collections import defaultdict

# 导入 Win32 API
try:
    import win32api
    import win32con
    WIN32_AVAILABLE = True
    print("✅ Win32 API 可用")
except ImportError:
    WIN32_AVAILABLE = False
    print("❌ Win32 API 不可用，请安装: pip install pywin32")
    sys.exit(1)

class GameJoystickController:
    def __init__(self):
        self.serial_port = None
        self.is_running = False
        self.key_states = defaultdict(bool)
        self.last_position = {"x": 0, "y": 0}
        
        # Windows 虚拟键码映射
        self.vk_codes = {
            'w': 0x57, 'a': 0x41, 's': 0x53, 'd': 0x44,
            'v': 0x56, 'space': 0x20, 'e': 0x45, 'f': 0x46,
            'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27
        }
        
        # 按键映射配置
        self.key_mapping = {
            "摇杆：上": "w",
            "摇杆：下": "s", 
            "摇杆：左": "a",
            "摇杆：右": "d",
            "摇杆：左上": ["w", "a"],
            "摇杆：右上": ["w", "d"],
            "摇杆：左下": ["s", "a"],
            "摇杆：右下": ["s", "d"],
            "摇杆按键按下": "v",
            "上按钮按下": "up",
            "下按钮按下": "down", 
            "左按钮按下": "left",
            "右按钮按下": "right",
            "E 按钮按下": "e",
            "F 按钮按下": "f",
        }
    
    def press_key_win32(self, key):
        """使用 Win32 API 按下按键"""
        if key not in self.vk_codes:
            print(f"❌ 未知按键: {key}")
            return False
        
        try:
            vk_code = self.vk_codes[key]
            # 发送按键按下事件
            win32api.keybd_event(vk_code, 0, 0, 0)
            print(f"🔽 Win32 按下: {key} (VK: 0x{vk_code:02X})")
            return True
        except Exception as e:
            print(f"❌ Win32 按键失败 {key}: {e}")
            return False
    
    def release_key_win32(self, key):
        """使用 Win32 API 释放按键"""
        if key not in self.vk_codes:
            return False
        
        try:
            vk_code = self.vk_codes[key]
            # 发送按键释放事件
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            print(f"🔼 Win32 释放: {key} (VK: 0x{vk_code:02X})")
            return True
        except Exception as e:
            print(f"❌ Win32 释放失败 {key}: {e}")
            return False
    
    def press_keys_continuous(self, keys):
        """按下按键（持续状态）"""
        if isinstance(keys, str):
            keys = [keys]
            
        for key in keys:
            if not self.key_states[key]:
                if self.press_key_win32(key):
                    self.key_states[key] = True
    
    def press_keys(self, keys):
        """按下按键（按钮事件）"""
        if isinstance(keys, str):
            keys = [keys]
            
        for key in keys:
            if self.press_key_win32(key):
                time.sleep(0.05)  # 短暂延迟
                self.release_key_win32(key)
    
    def release_keys(self, keys):
        """释放按键"""
        if isinstance(keys, str):
            keys = [keys]
            
        for key in keys:
            if self.key_states[key]:
                if self.release_key_win32(key):
                    self.key_states[key] = False
    
    def release_all_keys(self):
        """释放所有按键"""
        for key, pressed in self.key_states.items():
            if pressed:
                self.release_key_win32(key)
                self.key_states[key] = False
    
    def connect_serial(self, port="COM13", baudrate=115200):
        """连接串口"""
        try:
            self.serial_port = serial.Serial(port, baudrate, timeout=1)
            print(f"✅ 串口连接成功: {port} @ {baudrate}")
            time.sleep(2)
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def process_joystick_data(self, data):
        """处理摇杆数据"""
        data = data.strip()
        
        # 解析带时间戳的数据
        if " > " in data:
            _, actual_data = data.split(" > ", 1)
            data = actual_data.strip()
        
        # 忽略系统信息
        ignore_patterns = ["校准", "测试程序", "开始检测", "=", "正在", "完成"]
        if any(pattern in data for pattern in ignore_patterns):
            return
        
        # 处理位置数据
        if "摇杆位置" in data:
            self.handle_position_data(data)
            return
            
        print(f"📡 接收: {data}")
        
        # 检查按键映射
        if data in self.key_mapping:
            keys = self.key_mapping[data]
            if keys:
                if "按下" in data:
                    self.press_keys(keys)  # 按钮事件
                else:
                    self.press_keys_continuous(keys)  # 方向事件
    
    def handle_position_data(self, data):
        """处理位置数据"""
        try:
            if "X:" in data and "Y:" in data:
                x_start = data.find("X:") + 2
                comma_pos = data.find(",", x_start)
                y_start = data.find("Y:") + 2
                
                if comma_pos == -1:
                    return
                
                x_str = data[x_start:comma_pos].strip()
                y_str = data[y_start:].strip()
                
                x_pos = int(x_str)
                y_pos = int(y_str)
                
                # 死区检测
                dead_zone = 10
                if abs(x_pos) <= dead_zone and abs(y_pos) <= dead_zone:
                    # 释放所有方向键
                    direction_keys = ["w", "a", "s", "d"]
                    for key in direction_keys:
                        if self.key_states[key]:
                            self.release_keys(key)
                    print(f"🎯 摇杆回中: X={x_pos}, Y={y_pos}")
                else:
                    # 处理方向移动
                    self.handle_movement(x_pos, y_pos, dead_zone)
                    
        except Exception as e:
            print(f"⚠️  位置数据解析错误: {e}")
    
    def handle_movement(self, x_pos, y_pos, dead_zone):
        """处理移动"""
        # 先释放所有方向键
        direction_keys = ["w", "a", "s", "d"]
        for key in direction_keys:
            if self.key_states[key]:
                self.release_keys(key)
        
        # 确定需要按下的键
        keys_to_press = []
        
        if y_pos < -dead_zone:  # 向上
            keys_to_press.append("w")
        elif y_pos > dead_zone:  # 向下
            keys_to_press.append("s")
            
        if x_pos < -dead_zone:  # 向左
            keys_to_press.append("a")
        elif x_pos > dead_zone:  # 向右
            keys_to_press.append("d")
        
        # 按下相应的键
        if keys_to_press:
            self.press_keys_continuous(keys_to_press)
            print(f"🎮 移动: {'+'.join(keys_to_press)} (X={x_pos}, Y={y_pos})")
    
    def serial_listener(self):
        """串口监听线程"""
        print("🎮 开始监听摇杆数据...")
        
        while self.is_running:
            try:
                if self.serial_port and self.serial_port.is_open and self.serial_port.in_waiting:
                    data = self.serial_port.readline().decode('utf-8', errors='ignore')
                    if data:
                        self.process_joystick_data(data)
            except Exception as e:
                print(f"❌ 串口读取错误: {e}")
                break
                
            time.sleep(0.01)
    
    def start(self):
        """启动控制器"""
        print("=" * 50)
        print("🎮 JoystickController - 游戏专用版")
        print("使用 Win32 API 直接发送按键")
        print("=" * 50)
        
        if not self.connect_serial():
            return
        
        print("\n🎯 按键映射:")
        for action, keys in self.key_mapping.items():
            if keys:
                if isinstance(keys, list):
                    keys_str = " + ".join(keys)
                else:
                    keys_str = keys
                print(f"  {action} -> {keys_str}")
        
        print("\n⚠️  请确保以管理员身份运行以获得最佳兼容性")
        print("⌨️  按 Ctrl+C 退出")
        print("-" * 50)
        
        # 启动监听线程
        self.is_running = True
        listener_thread = threading.Thread(target=self.serial_listener)
        listener_thread.daemon = True
        listener_thread.start()
        
        try:
            while True:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n\n🛑 正在退出...")
            self.stop()
    
    def stop(self):
        """停止控制器"""
        self.is_running = False
        self.release_all_keys()
        
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            print("✅ 串口已关闭")
        
        print("✅ 控制器已停止")

def main():
    if not WIN32_AVAILABLE:
        print("❌ 需要 Win32 API 支持")
        print("请安装: pip install pywin32")
        sys.exit(1)
    
    controller = GameJoystickController()
    controller.start()

if __name__ == "__main__":
    main()
