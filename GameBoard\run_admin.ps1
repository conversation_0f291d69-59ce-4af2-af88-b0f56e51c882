# 摇杆控制器 - PowerShell 管理员启动脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   摇杆控制器 - 管理员权限启动" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Host "✅ 已获得管理员权限" -ForegroundColor Green
} else {
    Write-Host "❌ 需要管理员权限" -ForegroundColor Red
    Write-Host "请以管理员身份运行 PowerShell" -ForegroundColor Yellow
    Write-Host "按任意键退出..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""
Write-Host "正在启动摇杆控制器..." -ForegroundColor Yellow
Write-Host "当前目录: $PSScriptRoot" -ForegroundColor Gray
Write-Host ""

# 切换到脚本目录
Set-Location $PSScriptRoot

# 启动 Python 程序
try {
    python joystick_controller_final.py
} catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "程序已退出，按任意键关闭窗口..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
