#!/usr/bin/env python3
"""
输入问题诊断工具
帮助诊断为什么按键无法被游戏识别
"""

import time
import sys
import ctypes
import subprocess
import os

def check_admin():
    """检查管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def test_notepad():
    """在记事本中测试按键"""
    print("🔍 在记事本中测试按键...")
    
    try:
        # 启动记事本
        notepad = subprocess.Popen(['notepad.exe'])
        time.sleep(2)  # 等待记事本启动
        
        print("✅ 记事本已启动")
        print("⚠️  请确保记事本窗口处于活动状态")
        input("按 Enter 继续测试...")
        
        # 测试 keyboard 库
        try:
            import keyboard
            print("📝 使用 keyboard 库在记事本中输入 'Hello'...")
            keyboard.write('Hello from keyboard lib')
            keyboard.press_and_release('enter')
            time.sleep(1)
            return True
        except Exception as e:
            print(f"❌ keyboard 库测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 记事本测试失败: {e}")
        return False

def test_win32_notepad():
    """使用 Win32 API 在记事本中测试"""
    try:
        import win32api
        import win32con
        
        print("📝 使用 Win32 API 在记事本中测试...")
        
        # 发送一些字符
        chars = "Win32 Test"
        for char in chars:
            vk_code = ord(char.upper())
            win32api.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(0.05)
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            time.sleep(0.05)
        
        # 按 Enter
        win32api.keybd_event(0x0D, 0, 0, 0)  # 按下 Enter
        time.sleep(0.05)
        win32api.keybd_event(0x0D, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放 Enter
        
        return True
        
    except Exception as e:
        print(f"❌ Win32 API 记事本测试失败: {e}")
        return False

def check_game_compatibility():
    """检查游戏兼容性"""
    print("\n🎮 游戏兼容性检查")
    print("=" * 40)
    
    game_name = input("请输入您的游戏名称: ").strip()
    
    # 常见的有输入保护的游戏
    protected_games = [
        'valorant', 'csgo', 'cs2', 'pubg', 'fortnite', 'apex', 
        'overwatch', 'rainbow six', 'warzone', 'battlefield'
    ]
    
    is_protected = any(protected in game_name.lower() for protected in protected_games)
    
    if is_protected:
        print("⚠️  检测到可能有反作弊保护的游戏")
        print("建议解决方案:")
        print("1. 关闭游戏的'原始输入'设置")
        print("2. 在游戏设置中查找'允许第三方输入'")
        print("3. 考虑使用硬件级解决方案")
        print("4. 检查游戏是否在全屏独占模式")
    else:
        print("✅ 游戏通常应该支持模拟输入")
    
    return is_protected

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 40)
    
    print("1. 🔧 权限解决方案:")
    print("   - 以管理员身份运行 PowerShell")
    print("   - 右键点击 PowerShell -> '以管理员身份运行'")
    print("   - 然后运行测试程序")
    
    print("\n2. 🎮 游戏设置:")
    print("   - 将游戏设置为窗口化或无边框窗口模式")
    print("   - 关闭'原始输入'或'直接输入'")
    print("   - 查找'兼容性'或'输入'相关设置")
    
    print("\n3. 🔄 替代方案:")
    print("   - 使用 AutoHotkey 脚本")
    print("   - 考虑硬件级按键模拟器")
    print("   - 使用游戏手柄模拟")
    
    print("\n4. 🛠️ 硬件方案:")
    print("   - Arduino Leonardo (可模拟真实键盘)")
    print("   - USB HID 设备")
    print("   - 物理按键模拟器")

def create_admin_script():
    """创建管理员权限运行脚本"""
    script_content = '''@echo off
echo 正在以管理员权限运行测试...
cd /d "%~dp0"
python test_sendinput.py
pause
'''
    
    try:
        with open('run_as_admin.bat', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✅ 已创建 run_as_admin.bat")
        print("   右键点击此文件 -> '以管理员身份运行'")
        return True
    except Exception as e:
        print(f"❌ 创建脚本失败: {e}")
        return False

def main():
    print("=" * 50)
    print("🔍 输入问题诊断工具")
    print("=" * 50)
    
    # 检查权限
    print("1. 检查管理员权限...")
    if check_admin():
        print("✅ 当前以管理员身份运行")
    else:
        print("❌ 当前未以管理员身份运行")
    
    # 检查库可用性
    print("\n2. 检查输入库...")
    
    try:
        import keyboard
        print("✅ keyboard 库可用")
    except ImportError:
        print("❌ keyboard 库不可用")
    
    try:
        import win32api
        print("✅ win32api 库可用")
    except ImportError:
        print("❌ win32api 库不可用")
    
    # 记事本测试
    print("\n3. 基础功能测试")
    choice = input("是否在记事本中测试按键功能？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        notepad_success = test_notepad()
        if notepad_success:
            print("✅ 记事本测试成功 - 基础输入功能正常")
        else:
            print("❌ 记事本测试失败 - 可能有权限或库问题")
        
        # Win32 API 测试
        if not notepad_success:
            print("\n尝试 Win32 API 测试...")
            win32_success = test_win32_notepad()
            if win32_success:
                print("✅ Win32 API 测试成功")
            else:
                print("❌ Win32 API 测试也失败")
    
    # 游戏兼容性检查
    print("\n4. 游戏兼容性分析")
    is_protected = check_game_compatibility()
    
    # 建议解决方案
    suggest_solutions()
    
    # 创建管理员脚本
    print("\n5. 创建管理员权限脚本")
    create_admin_script()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")
    print("=" * 50)
    
    if not check_admin():
        print("⚠️  强烈建议以管理员身份重新运行测试")
        print("使用刚创建的 run_as_admin.bat 文件")

if __name__ == "__main__":
    main()
