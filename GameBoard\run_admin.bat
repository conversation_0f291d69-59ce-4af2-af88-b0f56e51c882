@echo off
title 摇杆控制器 - 管理员模式
echo ========================================
echo    摇杆控制器 - 管理员权限启动
echo ========================================
echo.
echo 正在检查管理员权限...

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 已获得管理员权限
) else (
    echo ❌ 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在启动摇杆控制器...
echo 当前目录: %~dp0
echo.

cd /d "%~dp0"
python joystick_controller_final.py

echo.
echo 程序已退出，按任意键关闭窗口...
pause
