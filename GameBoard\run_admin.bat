@echo off
title Joystick Controller - Admin Mode
echo ========================================
echo    Joystick Controller - Admin Mode
echo ========================================
echo.
echo Checking admin privileges...

:: Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running with admin privileges
) else (
    echo [ERROR] Admin privileges required
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Starting joystick controller...
echo Current directory: %~dp0
echo.

cd /d "%~dp0"
python joystick_controller_final.py

echo.
echo Program exited. Press any key to close...
pause
