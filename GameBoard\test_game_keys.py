#!/usr/bin/env python3
"""
游戏按键测试工具
直接测试 Win32 API 按键是否能被游戏识别
"""

import time
import sys

try:
    import win32api
    import win32con
    WIN32_AVAILABLE = True
    print("✅ Win32 API 可用")
except ImportError:
    WIN32_AVAILABLE = False
    print("❌ Win32 API 不可用，请安装: pip install pywin32")
    sys.exit(1)

class GameKeyTester:
    def __init__(self):
        # Windows 虚拟键码映射
        self.vk_codes = {
            'w': 0x57, 'a': 0x41, 's': 0x53, 'd': 0x44,
            'v': 0x56, 'space': 0x20, 'e': 0x45, 'f': 0x46,
            'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27
        }
    
    def press_key(self, key):
        """按下并释放按键"""
        if key not in self.vk_codes:
            print(f"❌ 未知按键: {key}")
            return False
        
        try:
            vk_code = self.vk_codes[key]
            print(f"🔽 按下: {key} (VK: 0x{vk_code:02X})")
            
            # 按下
            win32api.keybd_event(vk_code, 0, 0, 0)
            time.sleep(0.1)  # 保持按下状态
            
            # 释放
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            print(f"🔼 释放: {key}")
            
            return True
        except Exception as e:
            print(f"❌ 按键失败 {key}: {e}")
            return False
    
    def hold_key(self, key, duration=1.0):
        """持续按住按键"""
        if key not in self.vk_codes:
            print(f"❌ 未知按键: {key}")
            return False
        
        try:
            vk_code = self.vk_codes[key]
            print(f"🔽 按住: {key} (VK: 0x{vk_code:02X}) 持续 {duration} 秒")
            
            # 按下
            win32api.keybd_event(vk_code, 0, 0, 0)
            time.sleep(duration)
            
            # 释放
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            print(f"🔼 释放: {key}")
            
            return True
        except Exception as e:
            print(f"❌ 按键失败 {key}: {e}")
            return False
    
    def test_movement_combo(self):
        """测试组合移动（如左上）"""
        print("\n🎮 测试组合移动: 左上 (W+A)")
        
        try:
            # 同时按下 W 和 A
            w_vk = self.vk_codes['w']
            a_vk = self.vk_codes['a']
            
            print("🔽 按下: W+A")
            win32api.keybd_event(w_vk, 0, 0, 0)  # 按下 W
            win32api.keybd_event(a_vk, 0, 0, 0)  # 按下 A
            
            time.sleep(1.0)  # 保持1秒
            
            print("🔼 释放: W+A")
            win32api.keybd_event(w_vk, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放 W
            win32api.keybd_event(a_vk, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放 A
            
            return True
        except Exception as e:
            print(f"❌ 组合按键失败: {e}")
            return False
    
    def run_interactive_test(self):
        """交互式测试"""
        print("=" * 50)
        print("🎮 游戏按键测试工具 - Win32 API 版本")
        print("=" * 50)
        
        print("\n⚠️  请确保游戏窗口处于活动状态！")
        print("测试将发送以下按键到游戏中：")
        print("  - 单次按键测试")
        print("  - 持续按键测试")
        print("  - 组合按键测试")
        
        input("\n按 Enter 开始测试...")
        
        # 测试单次按键
        print("\n" + "="*30)
        print("📍 单次按键测试")
        print("="*30)
        
        test_keys = ['w', 'a', 's', 'd', 'v', 'e']
        for key in test_keys:
            print(f"\n测试按键: {key}")
            self.press_key(key)
            time.sleep(1)
        
        # 测试持续按键
        print("\n" + "="*30)
        print("📍 持续按键测试")
        print("="*30)
        
        for key in ['w', 'd']:  # 测试前进和右移
            print(f"\n测试持续按键: {key}")
            self.hold_key(key, 2.0)
            time.sleep(1)
        
        # 测试组合按键
        print("\n" + "="*30)
        print("📍 组合按键测试")
        print("="*30)
        
        self.test_movement_combo()
        
        print("\n" + "="*50)
        print("🎯 测试完成！")
        print("如果游戏中有响应，说明 Win32 API 方法有效")
        print("请使用 joystick_controller_game.py 连接摇杆")
        print("="*50)

def main():
    if not WIN32_AVAILABLE:
        print("❌ 需要 Win32 API 支持")
        print("请安装: pip install pywin32")
        sys.exit(1)
    
    tester = GameKeyTester()
    tester.run_interactive_test()

if __name__ == "__main__":
    main()
