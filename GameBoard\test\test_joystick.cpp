#include <unity.h>
#include <JoystickShield.h>

// 测试用的 JoystickShield 实例
JoystickShield testJoystick;

void setUp(void) {
    // 每个测试前的设置
    testJoystick.begin();
}

void tearDown(void) {
    // 每个测试后的清理
}

// 测试构造函数和初始化
void test_joystick_initialization(void) {
    JoystickShield js;
    // 测试对象创建成功
    TEST_ASSERT_TRUE(true); // 如果能到这里说明构造函数工作正常
}

// 测试摇杆校准
void test_joystick_calibration(void) {
    testJoystick.calibrateJoystick();
    // 校准后应该能正常工作
    TEST_ASSERT_TRUE(true);
}

// 测试引脚设置
void test_pin_configuration(void) {
    // 测试设置摇杆引脚
    testJoystick.setJoystickPins(A0, A1);
    
    // 测试设置按钮引脚
    testJoystick.setButtonPins(8, 2, 3, 4, 5, 7, 6);
    
    // 如果没有异常，测试通过
    TEST_ASSERT_TRUE(true);
}

// 测试死区设置
void test_dead_zone_setting(void) {
    testJoystick.setDeadZone(50);
    // 测试不同的死区值
    testJoystick.setDeadZone(0);
    testJoystick.setDeadZone(100);
    
    TEST_ASSERT_TRUE(true);
}

// 测试摇杆读取功能
void test_joystick_reading(void) {
    // 测试原始值读取
    int xRaw = testJoystick.xRaw();
    int yRaw = testJoystick.yRaw();
    
    // 原始值应该在合理范围内 (0-1023)
    TEST_ASSERT_TRUE(xRaw >= 0 && xRaw <= 1023);
    TEST_ASSERT_TRUE(yRaw >= 0 && yRaw <= 1023);
    
    // 测试幅度值读取
    int xAmp = testJoystick.xAmplitude();
    int yAmp = testJoystick.yAmplitude();
    
    // 幅度值应该在合理范围内 (-512 到 512)
    TEST_ASSERT_TRUE(xAmp >= -512 && xAmp <= 512);
    TEST_ASSERT_TRUE(yAmp >= -512 && yAmp <= 512);
}

// 测试方向检测功能
void test_direction_detection(void) {
    // 这个测试需要实际的硬件输入，这里只测试函数调用不会崩溃
    testJoystick.processEvents();
    
    // 测试所有方向检测函数
    bool up = testJoystick.isUp();
    bool down = testJoystick.isDown();
    bool left = testJoystick.isLeft();
    bool right = testJoystick.isRight();
    bool rightUp = testJoystick.isRightUp();
    bool rightDown = testJoystick.isRightDown();
    bool leftUp = testJoystick.isLeftUp();
    bool leftDown = testJoystick.isLeftDown();
    bool center = testJoystick.isCenter();
    bool notCenter = testJoystick.isNotCenter();
    
    // 中心和非中心状态应该是互斥的
    TEST_ASSERT_TRUE(center != notCenter);
}

// 测试按钮检测功能
void test_button_detection(void) {
    testJoystick.processEvents();
    
    // 测试所有按钮检测函数（不会崩溃即可）
    bool joystickBtn = testJoystick.isJoystickButton();
    bool upBtn = testJoystick.isUpButton();
    bool rightBtn = testJoystick.isRightButton();
    bool downBtn = testJoystick.isDownButton();
    bool leftBtn = testJoystick.isLeftButton();
    bool eBtn = testJoystick.isEButton();
    bool fBtn = testJoystick.isFButton();
    
    // 如果能执行到这里，说明所有按钮检测函数都正常工作
    TEST_ASSERT_TRUE(true);
}

// 测试工具函数
void test_utility_functions(void) {
    // 测试状态打印（不应该崩溃）
    testJoystick.printStatus();
    testJoystick.printCalibration();
    
    TEST_ASSERT_TRUE(true);
}

// 主测试函数
void setup() {
    delay(2000); // 等待串口稳定
    
    UNITY_BEGIN();
    
    RUN_TEST(test_joystick_initialization);
    RUN_TEST(test_joystick_calibration);
    RUN_TEST(test_pin_configuration);
    RUN_TEST(test_dead_zone_setting);
    RUN_TEST(test_joystick_reading);
    RUN_TEST(test_direction_detection);
    RUN_TEST(test_button_detection);
    RUN_TEST(test_utility_functions);
    
    UNITY_END();
}

void loop() {
    // 测试完成后什么都不做
}
