#!/usr/bin/env python3
"""
使用 SendInput API 的底层按键测试
这是 Windows 最底层的输入方法，绕过大多数游戏保护
"""

import time
import sys
import ctypes
from ctypes import wintypes, windll, c_uint, c_int, c_void_p, pointer, sizeof

# Windows API 常量
INPUT_KEYBOARD = 1
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_UNICODE = 0x0004

# 定义 Windows 结构体
class KEYBDINPUT(ctypes.Structure):
    _fields_ = [
        ("wVk", wintypes.WORD),
        ("wScan", wintypes.WORD),
        ("dwFlags", wintypes.DWORD),
        ("time", wintypes.DWORD),
        ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))
    ]

class INPUT(ctypes.Structure):
    class _INPUT(ctypes.Union):
        _fields_ = [("ki", KEYBDINPUT)]
    
    _anonymous_ = ("_input",)
    _fields_ = [
        ("type", wintypes.DWORD),
        ("_input", _INPUT)
    ]

class SendInputTester:
    def __init__(self):
        # 虚拟键码映射
        self.vk_codes = {
            'w': 0x57, 'a': 0x41, 's': 0x53, 'd': 0x44,
            'v': 0x56, 'space': 0x20, 'e': 0x45, 'f': 0x46,
            'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27
        }
        
        # 获取 SendInput 函数
        self.SendInput = windll.user32.SendInput
        self.SendInput.argtypes = [c_uint, ctypes.POINTER(INPUT), c_int]
        self.SendInput.restype = c_uint
    
    def send_key(self, vk_code, key_up=False):
        """发送单个按键事件"""
        extra = ctypes.c_ulong(0)
        ii_ = INPUT()
        ii_.type = INPUT_KEYBOARD
        ii_.ki.wVk = vk_code
        ii_.ki.wScan = 0
        ii_.ki.dwFlags = KEYEVENTF_KEYUP if key_up else 0
        ii_.ki.time = 0
        ii_.ki.dwExtraInfo = pointer(extra)
        
        result = self.SendInput(1, pointer(ii_), sizeof(ii_))
        return result == 1
    
    def press_key(self, key):
        """按下并释放按键"""
        if key not in self.vk_codes:
            print(f"❌ 未知按键: {key}")
            return False
        
        vk_code = self.vk_codes[key]
        
        try:
            print(f"🔽 SendInput 按下: {key} (VK: 0x{vk_code:02X})")
            
            # 按下
            if not self.send_key(vk_code, False):
                print(f"❌ 按下失败: {key}")
                return False
            
            time.sleep(0.1)  # 保持按下状态
            
            # 释放
            if not self.send_key(vk_code, True):
                print(f"❌ 释放失败: {key}")
                return False
            
            print(f"🔼 SendInput 释放: {key}")
            return True
            
        except Exception as e:
            print(f"❌ SendInput 失败 {key}: {e}")
            return False
    
    def hold_key(self, key, duration=1.0):
        """持续按住按键"""
        if key not in self.vk_codes:
            print(f"❌ 未知按键: {key}")
            return False
        
        vk_code = self.vk_codes[key]
        
        try:
            print(f"🔽 SendInput 按住: {key} (VK: 0x{vk_code:02X}) 持续 {duration} 秒")
            
            # 按下
            if not self.send_key(vk_code, False):
                print(f"❌ 按下失败: {key}")
                return False
            
            time.sleep(duration)
            
            # 释放
            if not self.send_key(vk_code, True):
                print(f"❌ 释放失败: {key}")
                return False
            
            print(f"🔼 SendInput 释放: {key}")
            return True
            
        except Exception as e:
            print(f"❌ SendInput 失败 {key}: {e}")
            return False
    
    def test_combo_keys(self):
        """测试组合按键"""
        print("\n🎮 测试组合移动: 左上 (W+A)")
        
        w_vk = self.vk_codes['w']
        a_vk = self.vk_codes['a']
        
        try:
            print("🔽 SendInput 按下: W+A")
            
            # 同时按下 W 和 A
            if not self.send_key(w_vk, False):
                return False
            if not self.send_key(a_vk, False):
                return False
            
            time.sleep(1.0)  # 保持1秒
            
            print("🔼 SendInput 释放: W+A")
            
            # 释放 W 和 A
            if not self.send_key(w_vk, True):
                return False
            if not self.send_key(a_vk, True):
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 组合按键失败: {e}")
            return False
    
    def run_test(self):
        """运行完整测试"""
        print("=" * 60)
        print("🎮 SendInput API 底层按键测试")
        print("这是 Windows 最底层的输入方法")
        print("=" * 60)
        
        print("\n⚠️  重要提示:")
        print("1. 请确保游戏窗口处于活动状态")
        print("2. 建议以管理员身份运行此程序")
        print("3. 如果游戏仍无响应，可能需要硬件级解决方案")
        
        input("\n按 Enter 开始测试...")
        
        # 测试单次按键
        print("\n" + "="*40)
        print("📍 SendInput 单次按键测试")
        print("="*40)
        
        test_keys = ['w', 'a', 's', 'd', 'v', 'e']
        success_count = 0
        
        for key in test_keys:
            print(f"\n测试按键: {key}")
            if self.press_key(key):
                success_count += 1
            time.sleep(1)
        
        print(f"\n✅ 单次按键测试完成: {success_count}/{len(test_keys)} 成功")
        
        # 测试持续按键
        print("\n" + "="*40)
        print("📍 SendInput 持续按键测试")
        print("="*40)
        
        for key in ['w', 'd']:
            print(f"\n测试持续按键: {key}")
            self.hold_key(key, 2.0)
            time.sleep(1)
        
        # 测试组合按键
        print("\n" + "="*40)
        print("📍 SendInput 组合按键测试")
        print("="*40)
        
        self.test_combo_keys()
        
        print("\n" + "="*60)
        print("🎯 SendInput 测试完成！")
        print("\n结果分析:")
        print("✅ 如果游戏有响应 -> SendInput 方法有效")
        print("❌ 如果游戏无响应 -> 游戏可能有强输入保护")
        print("\n下一步:")
        print("- 有响应: 使用 SendInput 版本的摇杆控制器")
        print("- 无响应: 考虑硬件级解决方案或游戏设置调整")
        print("="*60)

def check_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("🔍 检查运行权限...")
    if not check_admin():
        print("⚠️  警告: 未以管理员身份运行")
        print("建议以管理员身份运行以获得最佳效果")
        choice = input("是否继续？(y/n): ").lower().strip()
        if choice not in ['y', 'yes', '是']:
            print("程序退出")
            return
    else:
        print("✅ 以管理员身份运行")
    
    tester = SendInputTester()
    tester.run_test()

if __name__ == "__main__":
    main()
