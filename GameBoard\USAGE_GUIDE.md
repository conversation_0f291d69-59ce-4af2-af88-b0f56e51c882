# 🎮 JoystickShield PC 控制器 - 完整使用指南

## 📋 项目概述

这是一个完整的摇杆到键盘映射解决方案，让Arduino JoystickShield能够控制PC端的键盘输入。支持游戏、应用程序控制等多种场景。

## 🚀 快速开始

### 1. 硬件连接
- 将JoystickShield连接到Arduino
- 通过USB连接Arduino到PC
- 确保驱动程序已安装

### 2. 软件安装
```bash
# 自动安装依赖
install.bat

# 或手动安装
pip install pyserial keyboard
```

### 3. 启动控制器
```bash
# 一键启动
start.bat

# 或选择控制器版本
python joystick_controller.py      # 基础版
python advanced_controller.py      # 增强版
```

## 🎯 主要特性

### ✅ 已实现功能

#### 🔌 串口连接
- **COM13优先**: 自动尝试COM13，失败时自动检测其他端口
- **智能识别**: 自动识别Arduino设备(CH340/CP2102等)
- **容错处理**: 连接失败时提供手动选择选项

#### 🕹️ 摇杆控制  
- **8方向移动**: 支持上下左右及4个对角方向
- **智能回中**: 修复了回中时误触发相反按键的问题
- **死区检测**: 避免小幅震动引起的误操作
- **位置跟踪**: 实时显示摇杆位置信息

#### ⌨️ 按键映射
```
摇杆方向 → WASD移动键
摇杆按键 → 空格键  
方向按钮 → 方向键
E/F按钮 → E/F键
```

#### 🎮 高级功能 (advanced_controller.py)
- **多游戏模式**: FPS/方向键/多媒体/自定义模式
- **热键切换**: Ctrl+Shift+P切换模式
- **组合键支持**: 支持复杂按键组合
- **详细日志**: 完整的操作记录

#### 📊 数据处理
- **时间戳支持**: 兼容"16:18:54.901 > 摇杆：下"格式
- **异常处理**: 健壮的错误处理和恢复机制
- **实时反馈**: 按键状态和移动信息实时显示

## 🔧 最新修复

### 摇杆回中误触发问题 (已解决)
**问题**: 摇杆从上方回中时会误触发's'键  
**解决**: 添加智能回中检测
- 跟踪摇杆位置历史
- 识别回中过程趋势  
- 跳过回中时的移动触发

### 修复效果对比
```
修复前:
摇杆向上 → 按下w → 回中过程可能触发s ❌

修复后:  
摇杆向上 → 按下w → 回中过程跳过触发 → 只释放w ✅
```

## 📁 文件说明

### 主程序
- `joystick_controller.py` - 基础控制器
- `advanced_controller.py` - 增强版控制器
- `src/main.cpp` - Arduino端代码

### 配置文件
- `config.txt` - 自定义按键映射
- `platformio.ini` - PlatformIO配置
- `README.md` - 详细说明文档

### 辅助工具
- `install.bat` - 依赖安装脚本
- `start.bat` - 控制器启动脚本
- `test_com13.py` - COM13连接测试
- `test_parser.py` - 数据解析测试
- `monitor_joystick.py` - 行为监控工具
- `test_deadzone_fix.py` - 死区修复测试

## 🎮 使用技巧

### 串口设置
- 优先使用COM13，不可用时自动检测
- 支持手动选择串口
- 波特率: 115200

### 按键自定义  
编辑`config.txt`或直接修改代码中的`key_mapping`字典:
```python
self.key_mapping = {
    "摇杆：上": "w",      # 可改为其他键
    "摇杆：下": "s",
    # ... 更多映射
}
```

### 死区调整
```python
dead_zone = 10  # 调整死区大小，范围1-50
```

## ⚠️ 注意事项

### 系统要求
- Windows系统
- Python 3.6+
- 需要管理员权限(keyboard库要求)

### 常见问题
1. **串口连接失败**: 检查Arduino连接，确认COM端口
2. **按键无效**: 确保以管理员权限运行
3. **按键冲突**: 检查其他程序是否占用相同按键

## 📊 项目状态

### ✅ 完成功能
- [x] Arduino端JoystickShield集成
- [x] PC端串口通信
- [x] 基础摇杆移动映射  
- [x] 按钮功能映射
- [x] COM13优先连接
- [x] 时间戳数据解析
- [x] 多游戏模式支持
- [x] 回中误触发修复
- [x] 死区智能检测
- [x] 异常处理完善

### 🎯 项目状态: 完成 ✅

所有核心功能已实现并经过测试。摇杆控制器可以稳定运行，提供精确的键盘输入映射。

---

💡 **提示**: 如需进一步定制或有问题，可参考各个测试文件中的代码示例。
